

services:
  web:
    build: .
    command: >
      sh -c "python manage.py collectstatic --noinput &&
            gunicorn dcrm.wsgi:application --bind 0.0.0.0:8000 --workers 2"
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
    expose:
      - 8000

  scheduler_server:
    image: inngest/inngest
    ports:
      - 8288:8288
    command: inngest dev -u http://host.docker.internal:8000/api/inngest --no-discovery
    extra_hosts:
      - "host.docker.internal:host-gateway"

volumes:
  static_volume:
